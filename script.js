
        // --- <PERSON><PERSON> variabler ---
        const stores = [
            {"storeId": "7152", "namn": "POWER Torpavallen", "stad": "Göteborg", "adress": "Torpavallsgatan 4D", "lat": 57.6792, "lng": 11.9894},
            {"storeId": "7150", "namn": "POWER Högsbo", "stad": "Västra Frölunda", "adress": "Lona Knapes gata 1", "lat": 57.6348, "lng": 11.9059},
            {"storeId": "7151", "namn": "POWER Bäckebol", "stad": "<PERSON>ings Backa", "adress": "Transportgatan 19", "lat": 57.7461, "lng": 11.9094},
            {"storeId": "7156", "namn": "POWER Borås", "stad": "<PERSON><PERSON><PERSON><PERSON>", "adress": "Ålgårdsvägen 11", "lat": 57.7210, "lng": 12.9401},
            {"storeId": "7125", "namn": "<PERSON><PERSON><PERSON> Jönkö<PERSON>", "stad": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adress": "<PERSON><PERSON><PERSON>vägen 4A", "lat": 57.7826, "lng": 14.1618},
            {"storeId": "7155", "namn": "POWER Skövde", "stad": "Skövde", "adress": "Jonstorpsgatan 3C", "lat": 58.3875, "lng": 13.8458},
            {"storeId": "7147", "namn": "POWER Helsingborg Väla", "stad": "Ödåkra", "adress": "Marknadsvägen 5", "lat": 56.0776, "lng": 12.7441},
            {"storeId": "7170", "namn": "POWER Västerås", "stad": "Västerås", "adress": "Hallsta Gårdsgata 7", "lat": 59.6099, "lng": 16.5448},
            {"storeId": "7148", "namn": "POWER Lund", "stad": "Lund", "adress": "Avtalsvägen 2", "lat": 55.7047, "lng": 13.2900},
            {"storeId": "7149", "namn": "POWER Kristianstad", "stad": "Kristianstad", "adress": "Fundamentgatan 1", "lat": 56.0280, "lng": 14.1567},
            {"storeId": "7153", "namn": "POWER Linköping", "stad": "Linköping", "adress": "Björkgatan 4", "lat": 58.4108, "lng": 15.6214},
            {"storeId": "7154", "namn": "POWER Norrköping", "stad": "Norrköping", "adress": "Koppargatan 30", "lat": 58.5877, "lng": 16.1924},
            {"storeId": "7146", "namn": "POWER Malmö Svågertorp", "stad": "Malmö", "adress": "Nornegatan 8", "lat": 55.5636, "lng": 12.9719},
            {"storeId": "7157", "namn": "POWER Uppsala", "stad": "Uppsala", "adress": "Stångjärnsgatan 10", "lat": 59.8586, "lng": 17.6389},
            {"storeId": "7158", "namn": "POWER Örebro", "stad": "Örebro", "adress": "Bettorpsgatan 4", "lat": 59.2741, "lng": 15.2066},
            {"storeId": "7159", "namn": "POWER Sundsvall", "stad": "Sundsvall", "adress": "Norra Förmansvägen 18", "lat": 62.3908, "lng": 17.3069},
            {"storeId": "7160", "namn": "POWER Gävle", "stad": "Gävle", "adress": "Ingenjörsgatan 2", "lat": 60.6749, "lng": 17.1413},
            {"storeId": "7161", "namn": "POWER Stockholm Kungens Kurva", "stad": "Kungens Kurva", "adress": "Geometrivägen 1", "lat": 59.2465, "lng": 17.9414},
            {"storeId": "7162", "namn": "POWER Stockholm Barkarby", "stad": "Järfälla", "adress": "Herrestavägen 20", "lat": 59.4142, "lng": 17.8907}
        ];

        let ALL_PRODUCTS_FROM_API = []; 
        let displayedProductsCache = []; 

        let userLocation = null;
        let selectedStores = []; 
        let currentStoreFilterMode = 'closest'; 
        let currentSelectedCategory = null; 
        let showAllProductsInView = false; 
        let currentDisplayMode = 'compact';

        const locationStatusDiv = document.getElementById('location-status-text');
        const storeSelectorDiv = document.getElementById('store-selector');
        const loadButton = document.getElementById('load-button');
        const loadingDiv = document.getElementById('loading');
        const errorDiv = document.getElementById('error');
        const resultsDiv = document.getElementById('results');
        const categoryButtonsDiv = document.getElementById('category-buttons');
        const showAllToggleButton = document.getElementById('show-all-toggle');
        const viewModeToggleButton = document.getElementById('view-mode-toggle');
        const summaryContentDiv = document.getElementById('summary-content');
        const summarySectionDiv = document.getElementById('summary-section');
        
        // === VIKTIGT: Byt till din AKTUELLA Cloudflare Tunnel URL här! ===
        const API_BASE_URL = 'https://powerapi.rytterfalk.com'; // ERSÄTT OM DEN ÄNDRATS!
        // ======================================================

        const MODEL_KEYWORDS = {
            "Mac": ["MacBook Air 13", "MacBook Air 15", "MacBook Pro 13", "MacBook Pro 14", "MacBook Pro 16", "iMac 24", "iMac 27", "Mac mini", "Mac Studio", "Mac Pro", "MacBook Air", "MacBook Pro", "iMac", "M1", "M2", "M3", "M4"],
            "iPhone": ["iPhone 16 Pro Max", "iPhone 16 Pro", "iPhone 16 Plus", "iPhone 16e", "iPhone 16", "iPhone 15 Pro Max", "iPhone 15 Pro", "iPhone 15 Plus", "iPhone 15", "iPhone SE", "iPhone 14", "iPhone 13", "Pro Max", "Pro", "Plus"],
            "iPad": ["iPad Pro 13", "iPad Pro 12.9", "iPad Pro 11", "iPad Air 13", "iPad Air 11", "iPad Air", "iPad Mini", "iPad 10.9", "iPad (A16)","iPad 10.2", "iPad Pro", "iPad"],
            "Watch": ["Apple Watch Series 10 GPS + Cell 46 mm", "Apple Watch Series 10 GPS 46 mm",  "Apple Watch Series 10 GPS + Cell 42 mm", "Apple Watch Series 10 GPS 42", "Apple Watch Series 10", "Apple Watch Ultra 2", "Apple Watch Ultra", "Apple Watch Series 9", "Apple Watch Series 8", "Apple Watch Series 7", "Apple Watch SE GPS + Cell", "Apple Watch SE GPS","Apple Watch SE", "49mm", "45mm", "44mm", "41mm", "40mm", "42mm", "46mm"],
            "AirPods": ["AirPods Max", "AirPods Pro (2nd gen)", "AirPods Pro", "AirPods (3rd gen)", "AirPods (2nd gen)", "AirPods"],
            "ATV & HomePods": ["Apple TV 4K", "HomePod (2nd Gen)", "HomePod Mini", "Apple TV", "HomePod"],
            "Tillbehör": ["Magic Keyboard", "Magic Mouse", "Apple Pencil", "Smart Folio", "Adapter", "Kabel", "Laddare"]
        };

        function getProductSubGroup(title, category) {
            if (title && category && MODEL_KEYWORDS[category]) {
                for (const keyword of MODEL_KEYWORDS[category]) {
                    if (title.toLowerCase().includes(keyword.toLowerCase())) {
                        return keyword; 
                    }
                }
            }
            if (title && category === "Mac") { 
                if (title.toLowerCase().includes("macbook air")) return "MacBook Air";
                if (title.toLowerCase().includes("macbook pro")) return "MacBook Pro";
                if (title.toLowerCase().includes("imac")) return "iMac";
                if (title.toLowerCase().includes("mac mini")) return "Mac mini";
                if (title.toLowerCase().includes("mac studio")) return "Mac Studio";
            }
            return null; 
        }
        
        async function initializeApp() {
            console.log("initializeApp called");
            if(errorDiv) errorDiv.style.display = 'none';
            try {
                const productsApiUrl = `${API_BASE_URL}/api/products`;
                console.log("Hämtar produktlista från:", productsApiUrl);
                const response = await fetch(productsApiUrl);
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Kunde inte hämta produktlistan: ${response.status}. Svar: ${errorText.substring(0,100)}`);
                }
                const productsFromApi = await response.json();
                console.log("Produktlista mottagen från API:", productsFromApi.length, "produkter");

                if (productsFromApi && productsFromApi.length > 0) {
                    ALL_PRODUCTS_FROM_API = productsFromApi.map(p => ({
                        productId: p.productId ? p.productId.toString() : null, // Säkerställ sträng och hantera null
                        title: p.title || "Okänd Titel",
                        category: p.categoryName || "Okategoriserad",
                        ean: p.ean, 
                        sku: p.sku, 
                        price: p.price 
                    })).filter(p => p.productId); // Filtrera bort produkter utan ID
                } else { 
                    console.warn("Produktlistan från API var tom eller ogiltig.");
                    ALL_PRODUCTS_FROM_API = []; // Säkerställ att det är en array
                }
            } catch (err) {
                console.error("FEL vid hämtning av produktlista:", err);
                if(errorDiv) {
                    errorDiv.textContent = `Kunde inte ladda den initiala produktlistan: ${err.message}`;
                    errorDiv.style.display = 'block';
                }
                ALL_PRODUCTS_FROM_API = []; // Säkerställ att det är en array vid fel
            }
            renderCategoryButtons(); 
            selectCategory(null); 
            getUserLocation();       
        }

        function calculateDistance(lat1, lng1, lat2, lng2) {
            const R = 6371; const dLat = (lat2 - lat1) * Math.PI / 180; const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a)); return R * c;
        }

        function updateActiveButton(buttonClassCSSSelector, newActiveButtonId) {
            const buttonsInGroup = document.querySelectorAll(buttonClassCSSSelector);
            buttonsInGroup.forEach(btn => btn.classList.remove('active'));
            const activeButton = document.getElementById(newActiveButtonId);
            if (activeButton) activeButton.classList.add('active');
        }
        
        function renderCategoryButtons() {
            if (!categoryButtonsDiv) return;
            const uniqueCategories = [...new Set(ALL_PRODUCTS_FROM_API.map(p => p.category).filter(cat => cat))];
            const desiredCategoryOrder = ["Mac", "iPad", "iPhone", "Watch", "AirPods", "ATV & HomePods", "Tillbehör"];
            const sortedCategories = uniqueCategories.sort((a, b) => {
                const indexA = desiredCategoryOrder.indexOf(a); const indexB = desiredCategoryOrder.indexOf(b);
                if (indexA !== -1 && indexB !== -1) return indexA - indexB;
                if (indexA !== -1) return -1; if (indexB !== -1) return 1;
                return a.localeCompare(b);
            });
            categoryButtonsDiv.innerHTML = '';
            ['Alla Produkter', ...sortedCategories].forEach((categoryText) => {
                const categoryValue = categoryText === 'Alla Produkter' ? null : categoryText;
                const btn = document.createElement('button'); btn.textContent = categoryText;
                const btnIdSuffix = categoryValue ? categoryValue.replace(/\s|&|\//g, '-') : 'all';
                btn.id = `btn-cat-${btnIdSuffix}`;
                btn.onclick = () => selectCategory(categoryValue);
                if (currentSelectedCategory === categoryValue) btn.classList.add('active');
                categoryButtonsDiv.appendChild(btn);
            });
        }

        function selectCategory(categoryName) {
            currentSelectedCategory = categoryName;
            renderCategoryButtons(); 
            if (selectedStores.length > 0 || currentStoreFilterMode !== 'user_selected') {
                loadStockData();
            } else if (currentStoreFilterMode === 'user_selected' && resultsDiv) {
                resultsDiv.innerHTML = `<div style="grid-column: 1/-1; text-align:center; color:white; padding:50px;"><h3>Välj butiker för '${categoryName || "Alla produkter"}'</h3></div>`;
            }
        }

        function getUserLocation() {
            const btnClosest = document.getElementById('btn-closest');
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (p) => { userLocation = {lat:p.coords.latitude, lng:p.coords.longitude}; if(locationStatusDiv)locationStatusDiv.textContent=`Position hittad.`; setStoreFilter('closest',true); },
                    (e) => { console.error('Geo err:',e); if(locationStatusDiv)locationStatusDiv.textContent='Kunde inte hitta pos.'; if(btnClosest) btnClosest.textContent = 'Min närmaste (Position?)'; setStoreFilter('user_selected',false); }
                );
            } else { if(locationStatusDiv)locationStatusDiv.textContent='Geo stöds ej. Välj manuellt.'; if(btnClosest) btnClosest.textContent = 'Min närmaste (Geo stöds ej)'; setStoreFilter('user_selected',false); }
        }
        
        function setStoreFilter(mode, shouldLoadData = true) {
            currentStoreFilterMode = mode;
            updateActiveButton('.filter-buttons button', `btn-${mode}`);
            selectedStores = [];
            const btnClosest = document.getElementById('btn-closest');
            const btnNearby = document.getElementById('btn-nearby');
            if (mode !== 'closest' && btnClosest) btnClosest.textContent = 'Min närmaste';
            if (mode !== 'nearby' && btnNearby) btnNearby.textContent = 'Närliggande (50km)';

            if (!userLocation && (mode === 'closest' || mode === 'nearby')) {
                if(locationStatusDiv)locationStatusDiv.textContent = 'Position behövs.';
                currentStoreFilterMode = 'user_selected'; 
                updateActiveButton('.filter-buttons button', 'btn-user_selected');
                 if (btnClosest) btnClosest.textContent = 'Min närmaste (Position?)';
            }

            if (currentStoreFilterMode === 'closest' && userLocation) {
                if (stores.length === 0) { if(btnClosest) btnClosest.textContent = 'Närmaste (0)'; if(locationStatusDiv)locationStatusDiv.textContent = 'Inga butiker.'; return; }
                let closest = stores.reduce((prev, curr) => (calculateDistance(userLocation.lat, userLocation.lng, curr.lat, curr.lng) < calculateDistance(userLocation.lat, userLocation.lng, prev.lat, prev.lng) ? curr : prev), stores[0] || null);
                if (closest) { 
                    selectedStores = [closest.storeId.toString()]; 
                    if (btnClosest) btnClosest.textContent = `Visar: ${closest.namn}`;
                    if(locationStatusDiv)locationStatusDiv.textContent = `(${calculateDistance(userLocation.lat,userLocation.lng,closest.lat,closest.lng).toFixed(1)} km)`;
                } else {
                    if (btnClosest) btnClosest.textContent = 'Närmaste (Hittades ej)';
                    if(locationStatusDiv)locationStatusDiv.textContent = 'Kunde inte identifiera närmaste.';
                }
            } else if (currentStoreFilterMode === 'nearby' && userLocation) {
                const r=50;selectedStores=stores.filter(s=>calculateDistance(userLocation.lat,userLocation.lng,s.lat,s.lng)<=r).map(s=>s.storeId.toString());
                if(btnNearby)btnNearby.textContent=`Närliggande (${selectedStores.length}st)`;
                if(locationStatusDiv)locationStatusDiv.textContent=selectedStores.length>0?`Inom ${r}km.`:`Inga butiker inom ${r}km.`;
            } else if (currentStoreFilterMode === 'all_stores') {
                 selectedStores = stores.map(s => s.storeId.toString());
                 if(locationStatusDiv)locationStatusDiv.textContent = `Visar för alla ${selectedStores.length} butiker.`;
            } else { // user_selected
                if(locationStatusDiv)locationStatusDiv.textContent = 'Anpassat urval: Välj butiker.';
            }
            displayStores(); 
            updateLoadButton();
            if (shouldLoadData && selectedStores.length > 0) {
                loadStockData();
            } else if (shouldLoadData && selectedStores.length === 0 && currentStoreFilterMode !== 'user_selected') {
                if(resultsDiv) resultsDiv.innerHTML = `<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Inga butiker.</h3></div>`;
                if(loadButton) loadButton.disabled = true; 
            } else if (currentStoreFilterMode === 'user_selected' && selectedStores.length === 0 && shouldLoadData) {
                if(resultsDiv) resultsDiv.innerHTML = `<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Välj butiker.</h3></div>`;
            }
        }

        function displayStores() {
            if (currentStoreFilterMode !== 'user_selected') {
                if(storeSelectorDiv) storeSelectorDiv.style.display = 'none'; return;
            }
            if(!storeSelectorDiv) return; 
            storeSelectorDiv.style.display = 'grid';
            let sortedStores = [...stores];
            if (userLocation) {
                sortedStores.forEach(s => s.distance = calculateDistance(userLocation.lat, userLocation.lng, s.lat, s.lng));
                sortedStores.sort((a, b) => a.distance - b.distance);
            }
            storeSelectorDiv.innerHTML = ''; 
            sortedStores.forEach(store => {
                const storeCard = document.createElement('div');const sIdStr=store.storeId.toString();
                storeCard.className = `store-card ${selectedStores.includes(sIdStr)?'selected':''}`;
                storeCard.dataset.storeId = sIdStr; storeCard.onclick = () => toggleStore(store.storeId);
                let dText = store.distance ? `<div class="distance">${store.distance.toFixed(1)} km</div>` : '';
                storeCard.innerHTML = `<div class="store-name">${store.namn}</div><div class="store-address">${store.adress ? store.adress + ', ' : ''}${store.stad}</div>${dText}`;
                storeSelectorDiv.appendChild(storeCard);
            });
            updateLoadButton();
        }

        function toggleStore(storeId) {
            const sIdStr=storeId.toString();const idx=selectedStores.indexOf(sIdStr);
            if(idx>-1)selectedStores.splice(idx,1);else selectedStores.push(sIdStr);
            const card=storeSelectorDiv.querySelector(`[data-store-id="${sIdStr}"]`);
            if(card)card.classList.toggle('selected');
            if(selectedStores.length>0)loadStockData();
            else{if(resultsDiv)resultsDiv.innerHTML=`<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Välj butiker.</h3></div>`;}
            updateLoadButton(); 
        }

        function updateLoadButton() {
            if (!loadButton) return; 
            const btnClosest = document.getElementById('btn-closest');
            if (currentStoreFilterMode === 'closest') {
                const cS=selectedStores.length>0?stores.find(s=>s.storeId.toString()===selectedStores[0]):null;
                loadButton.textContent=cS?`Uppdatera för ${cS.namn}`:(userLocation?'Ingen närmaste':'Hitta närmaste');
            } else if (currentStoreFilterMode === 'nearby') {
                loadButton.textContent=`Uppdatera för närliggande (${selectedStores.length}st)`;
            } else if (currentStoreFilterMode === 'all_stores') {
                loadButton.textContent=`Uppdatera för alla ${selectedStores.length} butiker`;
            } else { 
                loadButton.textContent=selectedStores.length===0?'Välj butiker':'Ladda lager (${selectedStores.length} valda)`;
            }
            loadButton.disabled = selectedStores.length === 0;
        }
        
        function toggleDisplayMode() {
            if(!viewModeToggleButton) return;
            if (currentDisplayMode === 'compact') {
                currentDisplayMode = 'advanced';
                viewModeToggleButton.textContent = 'Kompakt vy'; 
                viewModeToggleButton.classList.add('active');
            } else {
                currentDisplayMode = 'compact';
                viewModeToggleButton.textContent = 'Avancerad vy'; 
                viewModeToggleButton.classList.remove('active');
            }
            console.log("Bytte visningsläge till:", currentDisplayMode);
            if (displayedProductsCache.length > 0) { displayResults(displayedProductsCache); updateSummaryView(displayedProductsCache); } 
            else { if (selectedStores.length > 0 || currentStoreFilterMode !== 'user_selected') { loadStockData();}}
        }

        function toggleShowAllProducts() {
            showAllProductsInView = !showAllProductsInView; 
            if(showAllToggleButton){
                showAllToggleButton.textContent = showAllProductsInView?"Visa endast i lager":"Visa även utan lager"; 
                showAllToggleButton.classList.toggle('active',showAllProductsInView);
            }
            if(displayedProductsCache.length>0)displayResults(displayedProductsCache);else{if(selectedStores.length>0)loadStockData();}
        }
        
        async function loadStockData() {
            console.log(`--- loadStockData (Kat: ${currentSelectedCategory||'Alla'}, Filter: ${currentStoreFilterMode}, Butiker: ${selectedStores.join(',')}) ---`);
            if(loadingDiv)loadingDiv.style.display='block'; if(errorDiv)errorDiv.style.display='none'; if(resultsDiv)resultsDiv.innerHTML='';
            if(ALL_PRODUCTS_FROM_API.length===0){console.warn("ALL_PRODUCTS_FROM_API är tom.");if(errorDiv){errorDiv.textContent="Produktkatalog ej laddad.";errorDiv.style.display='block';}if(loadingDiv)loadingDiv.style.display='none';return;}
            if(selectedStores.length===0){console.warn(`Inga valda butiker för: ${currentStoreFilterMode}`);if(resultsDiv)resultsDiv.innerHTML=`<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Inga butiker.</h3><p>Välj filter/butiker.</p></div>`;if(loadingDiv)loadingDiv.style.display='none';updateLoadButton();return;}
            
            const storeIdsParam=selectedStores.join(','); 
            const baseApiUrlForCurrentStock = `${API_BASE_URL}/api/current_stock`; 
            let qParams=[];
            if(storeIdsParam)qParams.push(`store_ids=${storeIdsParam}`);
            if(currentSelectedCategory)qParams.push(`category=${encodeURIComponent(currentSelectedCategory)}`);
            const fullApiUrl=`${baseApiUrlForCurrentStock}${qParams.length>0?'?':''}${qParams.join('&')}`;
            console.log("Anropar API:",fullApiUrl);

            try{
                const response=await fetch(fullApiUrl); 
                console.log(`Svar API status: ${response.status}`);
                if(!response.ok){const errTxt=await response.text();throw new Error(`API: ${response.status}. ${errTxt.substring(0,100)}`);}
                const apiData=await response.json();
                console.log("Data (current_stock):",apiData.length,"rader");
                
                displayedProductsCache=[];const productMap=new Map();
                const prodsToConsider=currentSelectedCategory?ALL_PRODUCTS_FROM_API.filter(p=>p.category===currentSelectedCategory):ALL_PRODUCTS_FROM_API;
                prodsToConsider.forEach(pS=>{productMap.set(pS.productId.toString(),{productId:pS.productId.toString(),title:pS.title,category:pS.category,price:pS.price,ean:pS.ean,sku:pS.sku,stocks:[]});});
                apiData.forEach(item=>{const pIdStr=item.productId.toString();if(productMap.has(pIdStr)){const sD=stores.find(s=>s.storeId.toString()===item.storeId.toString());const sN=sD?sD.namn:`Butik ${item.storeId}`;const sC=sD?sD.stad:'';productMap.get(pIdStr).stocks.push({store:{namn:sN,stad:sC,storeId:item.storeId},stock:item.stockCount});}});
                displayedProductsCache=Array.from(productMap.values()); 
                console.log("Cachad data:",displayedProductsCache.length,"produkter");
                displayResults(displayedProductsCache);
            }catch(err){
                console.error("Fel i loadStockData:",err);let msg=err.message;if(err instanceof SyntaxError)msg="Svar från servern felaktig JSON.";
                if(errorDiv){errorDiv.textContent=`Kunde ej hämta: ${msg}`;errorDiv.style.display='block';}
                if(resultsDiv)resultsDiv.innerHTML=`<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Fel</h3><p>${msg}</p></div>`;
            }finally{if(loadingDiv)loadingDiv.style.display='none';}
        }

        function displayResults(productsToDisplayInput) {
            if(!resultsDiv) return; resultsDiv.innerHTML = ''; let productsActuallyShown = 0;
            let productsToRender = JSON.parse(JSON.stringify(productsToDisplayInput));
            productsToRender.forEach(p => { p.subGroup = getProductSubGroup(p.title, p.category); });
            productsToRender.sort((a,b)=>{ const cA=a.category||"Ö";const cB=b.category||"Ö";const sA=a.subGroup||"~";const sB=b.subGroup||"~"; if(cA<cB)return-1;if(cA>cB)return 1;if(sA<sB)return-1;if(sA>sB)return 1;if(a.title<b.title)return-1;if(a.title>b.title)return 1;return 0;});
            let currentDisplayCat = "---"; let currentDisplaySub = "---";
            productsToRender.forEach(product => {
                const stocksForProductCard = showAllProductsInView ? product.stocks : product.stocks.filter(s => s.stock > 0);
                if (showAllProductsInView || stocksForProductCard.length > 0) {
                    if (product.category && product.category !== currentDisplayCat) {
                        currentDisplayCat = product.category; currentDisplaySub = "---"; 
                        const catH2=document.createElement('h2');catH2.textContent=currentDisplayCat; catH2.style.cssText="grid-column:1/-1;color:white;margin-top:30px;margin-bottom:15px;text-align:left;padding-bottom:5px;border-bottom:2px solid rgba(255,255,255,0.5);font-size:1.6rem;"; resultsDiv.appendChild(catH2);
                    }
                    if (product.subGroup && product.subGroup !== currentDisplaySub) {
                        currentDisplaySub = product.subGroup;
                        const subH3=document.createElement('h3'); subH3.textContent=currentDisplaySub; subH3.style.cssText="grid-column:1/-1;color:rgba(255,255,255,0.85);margin-top:15px;margin-bottom:8px;text-align:left;padding-left:10px;font-size:1.2rem;font-weight:500;"; resultsDiv.appendChild(subH3);
                    }
                    const pCard=document.createElement('div'); pCard.className='product-card';
                    if(!product.stocks.some(s=>s.stock>0)&&showAllProductsInView)pCard.classList.add('out-of-stock-card');
                    const sItemsHTML = product.stocks.map(s => `<div class="stock-item ${s.stock===0&&showAllProductsInView?'out-of-stock-item':''}"><div class="store-info"><div class="store-name-small">${s.store.namn}</div><div class="store-city">${s.store.stad}</div></div><div class="stock-count ${s.stock===0?'empty':''}">${s.stock} st</div></div>`).join('');
                    let detailsHTML = `<div class="product-details">Art.nr: ${product.productId}`;
                    if (product.ean && currentDisplayMode === 'advanced') detailsHTML += `    EAN: ${product.ean}`;
                    if (product.sku && currentDisplayMode === 'advanced') detailsHTML += `<br>SKU: ${product.sku}`;
                    if (typeof product.price === 'number' && product.price > 0 && currentDisplayMode === 'advanced') { detailsHTML += `<br>Pris: ${product.price.toLocaleString('sv-SE', { style: 'currency', currency: 'SEK' })}`;}
                    detailsHTML += `</div>`;
                    pCard.innerHTML=`<div class="product-title">${product.title}</div> ${detailsHTML} <div class="stock-info">${sItemsHTML.length > 0 ? sItemsHTML : (showAllProductsInView ? '<p style="font-style:italic;color:#777;text-align:center;">Inget lager.</p>' : '')}</div>`;
                    resultsDiv.appendChild(pCard); productsActuallyShown++;
                }
            });
            if(productsActuallyShown===0)resultsDiv.innerHTML=`<div style="grid-column:1/-1;text-align:center;color:white;padding:50px;"><h3>Inga produkter</h3><p>Inga produkter matchade${showAllProductsInView?'.':' med lager > 0.'}</p></div>`;
            else if (summarySectionDiv) updateSummaryView(productsToRender); // Kolla att summarySectionDiv finns
        }
        
        function updateSummaryView(productsToSummarize) {
            if (!summarySectionDiv || !summaryContentDiv) { console.error("Summerings-DOM-element saknas!"); return; } 
            if (!productsToSummarize || productsToSummarize.length === 0) {
                summarySectionDiv.style.display = 'none'; return;
            }
            let totalUnitsInStock = 0; let totalStockValue = 0; const uniqueProductIdsWithStock = new Set(); const stockPerStore = {}; 
            productsToSummarize.forEach(product => {
                let productHasLiveStockForSummary = false; const price = typeof product.price === 'number' ? product.price : null;
                product.stocks.forEach(stockItem => {
                    if (stockItem.stock > 0) {
                        totalUnitsInStock += stockItem.stock; productHasLiveStockForSummary = true; uniqueProductIdsWithStock.add(product.productId);
                        if (price !== null) totalStockValue += stockItem.stock * price;
                        const storeId = stockItem.store.storeId.toString();
                        if (!stockPerStore[storeId]) stockPerStore[storeId] = { name: stockItem.store.namn, city: stockItem.store.stad, units: 0, value: 0 };
                        stockPerStore[storeId].units += stockItem.stock;
                        if (price !== null) stockPerStore[storeId].value += stockItem.stock * price;
                    }
                });
            });
            const productsWithStockCount = uniqueProductIdsWithStock.size;
            let summaryHTML = `<dl class="summary-details-list">`;
            summaryHTML += `<dt>Totalt enheter i lager:</dt><dd>${totalUnitsInStock} st</dd>`;
            summaryHTML += `<dt>Antal unika produkter m. lager:</dt><dd>${productsWithStockCount}</dd>`;
            if (currentDisplayMode === 'advanced') {
                if (totalStockValue > 0) summaryHTML += `<dt>Uppskattat lagervärde:</dt><dd>${totalStockValue.toLocaleString('sv-SE',{style:'currency',currency:'SEK'})}</dd>`;
                summaryHTML += `</dl>`;
                if (Object.keys(stockPerStore).length > 1 && totalUnitsInStock > 0) {
                    summaryHTML += `<h4 style="margin-top:15px;font-size:1em;">Lagerfördelning per butik:</h4>`;
                    summaryHTML += `<table style="font-size:0.9em;width:100%;"><thead><tr><th>Butik</th><th>Enheter</th><th>Andel</th><th>Värde</th></tr></thead><tbody>`;
                    const sortedStoresByUnits=Object.entries(stockPerStore).sort(([,a],[,b])=>b.units-a.units);
                    for(const[storeId,storeData]of sortedStoresByUnits){const pcent=totalUnitsInStock>0?((storeData.units/totalUnitsInStock)*100).toFixed(1):"0.0";summaryHTML+=`<tr><td>${storeData.name} (${storeData.city})</td><td>${storeData.units} st</td><td>${pcent}%</td><td>${storeData.value>0?storeData.value.toLocaleString('sv-SE',{style:'currency',currency:'SEK'}):'-'}</td></tr>`;}
                    summaryHTML+=`</tbody></table>`;
                } else if (Object.keys(stockPerStore).length===1&&totalUnitsInStock>0){const sId=Object.keys(stockPerStore)[0];const sData=stockPerStore[sId];if(sData.value>0)summaryHTML+=`<p style="margin-top:10px;"><strong>Lagervärde (${sData.name}):</strong> ${sData.value.toLocaleString('sv-SE',{style:'currency',currency:'SEK'})}</p>`;}
            } else { summaryHTML += `</dl>`; if(Object.keys(stockPerStore).length>0&&totalUnitsInStock>0){summaryHTML+=`<p style="font-size:0.9em;margin-top:10px;">`;let sParts=[];for(const sId in stockPerStore){if(stockPerStore[sId].units>0)sParts.push(`${stockPerStore[sId].name}: ${stockPerStore[sId].units}st`);}summaryHTML+=sParts.join(' | ');summaryHTML+=`</p>`;}}
            summaryContentDiv.innerHTML = summaryHTML; summarySectionDiv.style.display = 'block';
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            if (viewModeToggleButton) { 
                viewModeToggleButton.onclick = () => toggleDisplayMode(); 
            } else {
                console.warn("Kunde inte hitta view-mode-toggle knappen.");
            }
            initializeApp(); 
        });